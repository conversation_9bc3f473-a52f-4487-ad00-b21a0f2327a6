<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ATMA API Documentation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark.min.css">
</head>
<body class="bg-gray-50 font-sans">
    <div id="app">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                                <span class="text-white font-bold text-sm">A</span>
                            </div>
                            <h1 class="text-xl font-bold text-gray-900">ATMA API Documentation</h1>
                        </div>
                        <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2.5 py-0.5 rounded-full">v1.0.0</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button id="searchBtn" class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </button>
                        <button id="themeToggle" class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Search Modal -->
        <div id="searchModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
            <div class="flex items-start justify-center min-h-screen pt-16 px-4">
                <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl">
                    <div class="p-4">
                        <input type="text" id="searchInput" placeholder="Search API endpoints..." 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                    </div>
                    <div id="searchResults" class="max-h-96 overflow-y-auto border-t border-gray-200">
                        <!-- Search results will be populated here -->
                    </div>
                </div>
            </div>
        </div>

        <div class="flex">
            <!-- Sidebar -->
            <aside class="w-80 bg-white shadow-sm border-r border-gray-200 h-screen sticky top-16 overflow-y-auto">
                <nav class="p-6">
                    <div class="space-y-2" id="navigation">
                        <!-- Navigation will be populated by JavaScript -->
                    </div>
                </nav>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 max-w-none">
                <div class="max-w-4xl mx-auto px-6 py-8">
                    <div id="content" class="prose prose-lg max-w-none">
                        <!-- Content will be populated by JavaScript -->
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script type="module" src="/src/main.js"></script>
</body>
</html>
